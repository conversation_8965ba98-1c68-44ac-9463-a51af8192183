<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射管理器测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>字段映射管理器 - 阶段一测试</h1>
    <div id="testResults"></div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="../src/js/core/smartoffice-core.js"></script>
    <script src="../src/js/core/smartoffice-events.js"></script>
    <script src="../src/js/core/smartoffice-storage.js"></script>
    <script src="../src/js/utils/smartoffice-helpers.js"></script>
    <script src="../src/js/data/smartoffice-field-mapper.js"></script>
    <script src="../src/js/data/smartoffice-timerange-manager.js"></script>

    <script>
        /**
         * @function 运行字段映射管理器测试
         */
        function runTests() {
            const results = [];
            
            // 测试1: 模块加载
            results.push(testModuleLoading());
            
            // 测试2: 字段映射建议
            results.push(testFieldMappingSuggestions());
            
            // 测试3: 映射模板管理
            results.push(testMappingTemplates());
            
            // 测试4: 时间段管理
            results.push(testTimeRangeManagement());
            
            // 测试5: 字段相似度计算
            results.push(testFieldSimilarity());
            
            // 显示测试结果
            displayResults(results);
        }

        /**
         * @function 测试模块加载
         */
        function testModuleLoading() {
            try {
                const fieldMapperExists = typeof SmartOffice?.Data?.FieldMapper !== 'undefined';
                const timeRangeManagerExists = typeof SmartOffice?.Data?.TimeRangeManager !== 'undefined';
                const standardFieldsCount = SmartOffice.Data.FieldMapper.standardFields.length;
                const presetTimeRangesCount = SmartOffice.Data.TimeRangeManager.presetTimeRanges.length;
                
                return {
                    name: '模块加载测试',
                    passed: fieldMapperExists && timeRangeManagerExists && standardFieldsCount >= 10 && presetTimeRangesCount >= 5,
                    details: `字段映射器: ${fieldMapperExists ? '✓' : '✗'}, 时间段管理器: ${timeRangeManagerExists ? '✓' : '✗'}, 标准字段: ${standardFieldsCount}个, 预设时间段: ${presetTimeRangesCount}个`
                };
            } catch (error) {
                return { name: '模块加载测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试字段映射建议
         */
        function testFieldMappingSuggestions() {
            try {
                const testFields = ['销售金额', '产品名称', '客户姓名', '订单日期', '数量'];
                const suggestions = SmartOffice.Data.FieldMapper.autoSuggestMapping(testFields);
                
                const hasHighConfidenceSuggestions = suggestions.some(s => s.confidence >= 0.8);
                const allFieldsHaveSuggestions = suggestions.length === testFields.length;
                
                return {
                    name: '智能映射建议测试',
                    passed: suggestions.length > 0 && hasHighConfidenceSuggestions,
                    details: `生成${suggestions.length}个建议, 高置信度建议: ${hasHighConfidenceSuggestions ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '智能映射建议测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试映射模板管理
         */
        function testMappingTemplates() {
            try {
                const template = {
                    name: '测试模板',
                    mappings: [
                        { sourceField: '销售金额', targetField: 'amount', dataType: 'number' },
                        { sourceField: '产品名称', targetField: 'product', dataType: 'string' }
                    ]
                };
                
                // 保存模板
                const saved = SmartOffice.Data.FieldMapper.saveMappingTemplate(template);
                
                // 获取模板
                const templates = SmartOffice.Data.FieldMapper.getAllMappingTemplates();
                const retrieved = templates.length > 0 ? SmartOffice.Data.FieldMapper.getMappingTemplate(templates[0].id) : null;
                
                // 应用模板
                const sourceFields = ['销售金额', '产品名称', '其他字段'];
                const mappedFields = SmartOffice.Data.FieldMapper.applyMappingTemplate(sourceFields, template);
                const hasMappedFields = mappedFields.some(f => f.mapped === true);
                
                return {
                    name: '映射模板管理测试',
                    passed: saved && retrieved && hasMappedFields,
                    details: `保存: ${saved ? '✓' : '✗'}, 获取: ${retrieved ? '✓' : '✗'}, 应用: ${hasMappedFields ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '映射模板管理测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试时间段管理
         */
        function testTimeRangeManagement() {
            try {
                const timeRangeManager = SmartOffice.Data.TimeRangeManager;
                
                // 创建自定义时间段
                const customId = timeRangeManager.createGlobalTimeRange({
                    name: '测试时间段',
                    start: '10:00',
                    end: '11:00'
                });
                
                // 获取时间段
                const timeRange = timeRangeManager.getGlobalTimeRange(customId);
                
                // 测试时间解析
                const parsedTime = timeRangeManager.parseTimeValue('10:30');
                
                // 测试时间范围检查
                const inRange = timeRangeManager.isTimeInRange('10:30', '10:00', '11:00');
                const outOfRange = timeRangeManager.isTimeInRange('09:30', '10:00', '11:00');
                
                return {
                    name: '时间段管理测试',
                    passed: customId && timeRange && parsedTime === '10:30' && inRange && !outOfRange,
                    details: `创建: ${customId ? '✓' : '✗'}, 获取: ${timeRange ? '✓' : '✗'}, 解析: ${parsedTime}, 范围检查: ${inRange && !outOfRange ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '时间段管理测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试字段相似度计算
         */
        function testFieldSimilarity() {
            try {
                const fieldMapper = SmartOffice.Data.FieldMapper;
                
                // 测试完全匹配
                const exactMatch = fieldMapper.calculateStringSimilarity('amount', 'amount');
                
                // 测试包含关系
                const containsMatch = fieldMapper.calculateStringSimilarity('销售金额', '金额');
                
                // 测试相似度
                const similarMatch = fieldMapper.calculateMappingConfidence('销售金额', 'amount');
                
                // 测试不匹配
                const noMatch = fieldMapper.calculateStringSimilarity('完全不同', 'totally different');
                
                return {
                    name: '字段相似度计算测试',
                    passed: exactMatch === 1 && containsMatch >= 0.8 && similarMatch > 0.5 && noMatch < 0.5,
                    details: `完全匹配: ${exactMatch}, 包含匹配: ${containsMatch.toFixed(2)}, 相似匹配: ${similarMatch.toFixed(2)}, 不匹配: ${noMatch.toFixed(2)}`
                };
            } catch (error) {
                return { name: '字段相似度计算测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 显示测试结果
         */
        function displayResults(results) {
            const container = document.getElementById('testResults');
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;
            
            let html = `<div class="test-result info">
                <strong>测试总结: ${passedCount}/${totalCount} 通过</strong>
            </div>`;
            
            results.forEach(result => {
                html += `<div class="test-result ${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.name}:</strong> ${result.passed ? '通过' : '失败'}<br>
                    <small>${result.details}</small>
                </div>`;
            });
            
            container.innerHTML = html;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
