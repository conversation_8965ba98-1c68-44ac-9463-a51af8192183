<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏重构与时间段管理集成测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .details { margin-top: 5px; font-size: 12px; opacity: 0.8; }
        .test-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .demo-button { 
            background: #007aff; color: white; border: none; border-radius: 8px; 
            padding: 10px 20px; margin: 5px; cursor: pointer; 
        }
        .demo-button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>导航栏重构与时间段管理集成 - 阶段四测试</h1>
    <div id="testResults"></div>

    <!-- 功能演示区域 -->
    <div class="test-container">
        <h3>功能演示</h3>
        <button class="demo-button" onclick="testNavigationBar()">测试重构后的导航栏</button>
        <button class="demo-button" onclick="testTimeRangeSelector()">测试时间段选择器</button>
        <button class="demo-button" onclick="testTimeRangeDialog()">测试时间段配置对话框</button>
        <button class="demo-button" onclick="testFixedPosition()">测试固定定位</button>
        <button class="demo-button" onclick="testResponsiveDesign()">测试响应式设计</button>
        <button class="demo-button" onclick="testZIndexLayers()">测试层级关系</button>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="../src/js/core/smartoffice-core.js"></script>
    <script src="../src/js/core/smartoffice-events.js"></script>
    <script src="../src/js/core/smartoffice-storage.js"></script>
    <script src="../src/js/core/smartoffice-router.js"></script>
    <script src="../src/js/utils/smartoffice-helpers.js"></script>
    <script src="../src/js/data/smartoffice-data-schemas.js"></script>
    <script src="../src/js/data/smartoffice-timerange-manager.js"></script>
    <script src="../src/js/components/smartoffice-navigation-bar.js"></script>
    <script src="../src/js/components/smartoffice-timerange-dialog.js"></script>

    <!-- 引入CSS样式 -->
    <link rel="stylesheet" href="../src/css/components/navigation-bar-enhanced.css">
    <link rel="stylesheet" href="../src/css/components/timerange-dialog.css">

    <script>
        /**
         * @function 运行导航栏重构测试
         */
        function runTests() {
            const results = [];
            
            // 测试1: 组件加载
            results.push(testComponentLoading());
            
            // 测试2: CSS样式加载
            results.push(testCSSLoading());
            
            // 测试3: 导航栏组件功能
            results.push(testNavigationBarComponent());
            
            // 测试4: 时间段对话框组件
            results.push(testTimeRangeDialogComponent());
            
            // 测试5: 固定定位
            results.push(testFixedPositioning());
            
            // 测试6: iOS设计规范
            results.push(testIOSDesignCompliance());
            
            // 测试7: 响应式设计
            results.push(testResponsiveDesign());
            
            // 测试8: z-index层级
            results.push(testZIndexLayers());
            
            // 显示测试结果
            displayResults(results);
        }

        /**
         * @function 测试组件加载
         */
        function testComponentLoading() {
            try {
                const navigationBarExists = typeof SmartOffice?.Components?.NavigationBar !== 'undefined';
                const timeRangeDialogExists = typeof SmartOffice?.Components?.TimeRangeDialog !== 'undefined';
                const timeRangeManagerExists = typeof SmartOffice?.Data?.TimeRangeManager !== 'undefined';
                const routerExists = typeof SmartOffice?.Core?.Router !== 'undefined';
                
                return {
                    name: '组件加载测试',
                    passed: navigationBarExists && timeRangeDialogExists && timeRangeManagerExists && routerExists,
                    details: `导航栏组件: ${navigationBarExists ? '✓' : '✗'}, 时间段对话框: ${timeRangeDialogExists ? '✓' : '✗'}, 时间段管理器: ${timeRangeManagerExists ? '✓' : '✗'}, 路由器: ${routerExists ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '组件加载测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试CSS样式加载
         */
        function testCSSLoading() {
            try {
                const stylesheets = document.styleSheets;
                let navigationBarCSSLoaded = false;
                let timeRangeDialogCSSLoaded = false;

                for (let i = 0; i < stylesheets.length; i++) {
                    const stylesheet = stylesheets[i];
                    if (stylesheet.href && stylesheet.href.includes('navigation-bar-enhanced.css')) {
                        navigationBarCSSLoaded = true;
                    }
                    if (stylesheet.href && stylesheet.href.includes('timerange-dialog.css')) {
                        timeRangeDialogCSSLoaded = true;
                    }
                }

                return {
                    name: 'CSS样式加载测试',
                    passed: navigationBarCSSLoaded && timeRangeDialogCSSLoaded,
                    details: `导航栏样式: ${navigationBarCSSLoaded ? '✓' : '✗'}, 时间段对话框样式: ${timeRangeDialogCSSLoaded ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: 'CSS样式加载测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试导航栏组件
         */
        function testNavigationBarComponent() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.className = 'nav-bar';
                testContainer.style.position = 'relative';
                testContainer.style.visibility = 'hidden';
                document.body.appendChild(testContainer);

                // 初始化组件
                const component = new SmartOffice.Components.NavigationBar({
                    enableTimeRangeManagement: true,
                    enableFieldMappingAccess: true
                });
                component.container = testContainer;
                component.rebuildNavigationBar();
                component.cacheElements();

                // 检查组件是否正确初始化
                const hasTimeRangeButton = !!testContainer.querySelector('.nav-time-range');
                const hasFieldMappingButton = !!testContainer.querySelector('.nav-field-mapping');
                const hasTimeRangeSelector = !!testContainer.querySelector('.time-range-selector');
                const hasTitle = !!testContainer.querySelector('.nav-title');

                // 清理
                document.body.removeChild(testContainer);

                return {
                    name: '导航栏组件测试',
                    passed: hasTimeRangeButton && hasFieldMappingButton && hasTimeRangeSelector && hasTitle,
                    details: `时间段按钮: ${hasTimeRangeButton ? '✓' : '✗'}, 字段映射按钮: ${hasFieldMappingButton ? '✓' : '✗'}, 时间段选择器: ${hasTimeRangeSelector ? '✓' : '✗'}, 标题: ${hasTitle ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '导航栏组件测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试时间段对话框组件
         */
        function testTimeRangeDialogComponent() {
            try {
                // 初始化组件
                const component = new SmartOffice.Components.TimeRangeDialog({
                    onSave: function() {},
                    onCancel: function() {}
                });
                component.init();

                // 检查组件是否正确初始化
                const hasDialog = !!component.dialog;
                const hasOverlay = !!component.overlay;
                const hasForm = !!component.dialog.querySelector('#timeRangeForm');
                const hasPreview = !!component.dialog.querySelector('#timeRangePreview');

                // 清理
                if (component.overlay && component.overlay.parentNode) {
                    component.overlay.parentNode.removeChild(component.overlay);
                }

                return {
                    name: '时间段对话框组件测试',
                    passed: hasDialog && hasOverlay && hasForm && hasPreview,
                    details: `对话框: ${hasDialog ? '✓' : '✗'}, 遮罩层: ${hasOverlay ? '✓' : '✗'}, 表单: ${hasForm ? '✓' : '✗'}, 预览: ${hasPreview ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '时间段对话框组件测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试固定定位
         */
        function testFixedPositioning() {
            try {
                // 创建测试导航栏
                const testNav = document.createElement('div');
                testNav.className = 'nav-bar';
                testNav.style.visibility = 'hidden';
                document.body.appendChild(testNav);

                const computedStyle = window.getComputedStyle(testNav);
                const position = computedStyle.position;
                const zIndex = parseInt(computedStyle.zIndex);
                const top = computedStyle.top;

                // 清理
                document.body.removeChild(testNav);

                const isFixed = position === 'fixed';
                const hasHighZIndex = zIndex >= 1000;
                const isAtTop = top === '0px';

                return {
                    name: '固定定位测试',
                    passed: isFixed && hasHighZIndex && isAtTop,
                    details: `固定定位: ${isFixed ? '✓' : '✗'}, z-index(${zIndex}): ${hasHighZIndex ? '✓' : '✗'}, 顶部位置: ${isAtTop ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '固定定位测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试iOS设计规范
         */
        function testIOSDesignCompliance() {
            try {
                // 创建测试按钮
                const testButton = document.createElement('button');
                testButton.className = 'nav-button';
                testButton.style.position = 'absolute';
                testButton.style.left = '-9999px';
                document.body.appendChild(testButton);

                const computedStyle = window.getComputedStyle(testButton);
                const width = parseInt(computedStyle.width);
                const height = parseInt(computedStyle.height);
                const borderRadius = parseInt(computedStyle.borderRadius);

                // 检查44px最小触摸区域
                const meetsMinTouchArea = width >= 44 && height >= 44;
                
                // 检查圆角设计
                const hasRoundedCorners = borderRadius >= 8;

                // 清理
                document.body.removeChild(testButton);

                return {
                    name: 'iOS设计规范测试',
                    passed: meetsMinTouchArea && hasRoundedCorners,
                    details: `最小触摸区域(${width}x${height}): ${meetsMinTouchArea ? '✓' : '✗'}, 圆角设计(${borderRadius}px): ${hasRoundedCorners ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: 'iOS设计规范测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试响应式设计
         */
        function testResponsiveDesign() {
            try {
                // 模拟不同屏幕尺寸
                const originalWidth = window.innerWidth;
                
                // 检查CSS媒体查询
                const mobileQuery = window.matchMedia('(max-width: 768px)');
                const tabletQuery = window.matchMedia('(max-width: 1024px)');
                
                const supportsMobileQuery = mobileQuery.matches !== undefined;
                const supportsTabletQuery = tabletQuery.matches !== undefined;

                return {
                    name: '响应式设计测试',
                    passed: supportsMobileQuery && supportsTabletQuery,
                    details: `移动端查询: ${supportsMobileQuery ? '✓' : '✗'}, 平板查询: ${supportsTabletQuery ? '✓' : '✗'}, 当前宽度: ${originalWidth}px`
                };
            } catch (error) {
                return { name: '响应式设计测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试z-index层级
         */
        function testZIndexLayers() {
            try {
                // 创建测试元素
                const testNav = document.createElement('div');
                testNav.className = 'nav-bar';
                testNav.style.visibility = 'hidden';
                document.body.appendChild(testNav);

                const testSelector = document.createElement('div');
                testSelector.className = 'time-range-selector';
                testSelector.style.visibility = 'hidden';
                document.body.appendChild(testSelector);

                const testDialog = document.createElement('div');
                testDialog.className = 'timerange-dialog-overlay';
                testDialog.style.visibility = 'hidden';
                document.body.appendChild(testDialog);

                // 获取z-index值
                const navZIndex = parseInt(window.getComputedStyle(testNav).zIndex) || 0;
                const selectorZIndex = parseInt(window.getComputedStyle(testSelector).zIndex) || 0;
                const dialogZIndex = parseInt(window.getComputedStyle(testDialog).zIndex) || 0;

                // 清理
                document.body.removeChild(testNav);
                document.body.removeChild(testSelector);
                document.body.removeChild(testDialog);

                // 检查层级关系
                const correctNavLayer = navZIndex >= 1000;
                const correctSelectorLayer = selectorZIndex > navZIndex;
                const correctDialogLayer = dialogZIndex > selectorZIndex;

                return {
                    name: 'z-index层级测试',
                    passed: correctNavLayer && correctSelectorLayer && correctDialogLayer,
                    details: `导航栏(${navZIndex}): ${correctNavLayer ? '✓' : '✗'}, 选择器(${selectorZIndex}): ${correctSelectorLayer ? '✓' : '✗'}, 对话框(${dialogZIndex}): ${correctDialogLayer ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: 'z-index层级测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 显示测试结果
         */
        function displayResults(results) {
            const container = document.getElementById('testResults');
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;
            
            let html = `<div class="test-result info">
                <strong>测试总结: ${passedCount}/${totalCount} 通过</strong>
                <div class="details">阶段四：导航栏重构与时间段管理集成功能验证</div>
            </div>`;
            
            results.forEach(result => {
                html += `<div class="test-result ${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.name}:</strong> ${result.passed ? '通过' : '失败'}
                    <div class="details">${result.details}</div>
                </div>`;
            });
            
            container.innerHTML = html;
        }

        /**
         * @function 演示功能
         */
        function testNavigationBar() {
            alert('导航栏重构测试：请在主应用中查看重构后的导航栏效果');
        }

        function testTimeRangeSelector() {
            alert('时间段选择器测试：请在主应用导航栏中点击时间段管理按钮');
        }

        function testTimeRangeDialog() {
            try {
                const dialog = new SmartOffice.Components.TimeRangeDialog({
                    onSave: function(timeRange) {
                        alert('时间段保存成功: ' + timeRange.name);
                    },
                    onCancel: function() {
                        alert('对话框已取消');
                    }
                });
                dialog.init();
                dialog.show();
            } catch (error) {
                alert('时间段对话框测试失败: ' + error.message);
            }
        }

        function testFixedPosition() {
            const scrollY = window.scrollY;
            window.scrollTo(0, 100);
            setTimeout(() => {
                alert(`固定定位测试：页面滚动到100px，导航栏应保持在顶部固定位置`);
                window.scrollTo(0, scrollY);
            }, 500);
        }

        function testResponsiveDesign() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            const isTablet = width > 768 && width <= 1024;
            const isDesktop = width > 1024;
            
            alert(`响应式设计测试:\n当前屏幕宽度: ${width}px\n设备类型: ${isMobile ? '移动端' : isTablet ? '平板' : '桌面端'}\n请调整浏览器窗口大小测试响应式效果`);
        }

        function testZIndexLayers() {
            alert('z-index层级测试：导航栏(1000) < 时间段选择器(1001) < 对话框(10001)');
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
