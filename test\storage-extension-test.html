<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储系统扩展测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .details { margin-top: 5px; font-size: 12px; opacity: 0.8; }
    </style>
</head>
<body>
    <h1>存储系统扩展 - 阶段二测试</h1>
    <div id="testResults"></div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="../src/js/core/smartoffice-core.js"></script>
    <script src="../src/js/core/smartoffice-events.js"></script>
    <script src="../src/js/core/smartoffice-storage.js"></script>
    <script src="../src/js/utils/smartoffice-helpers.js"></script>
    <script src="../src/js/data/smartoffice-data-schemas.js"></script>
    <script src="../src/js/data/smartoffice-field-mapper.js"></script>
    <script src="../src/js/data/smartoffice-timerange-manager.js"></script>

    <script>
        /**
         * @function 运行存储系统扩展测试
         */
        function runTests() {
            const results = [];
            
            // 测试1: 数据结构定义模块
            results.push(testDataSchemas());
            
            // 测试2: 扩展存储方法
            results.push(testExtendedStorageMethods());
            
            // 测试3: 数据验证功能
            results.push(testDataValidation());
            
            // 测试4: 数据迁移功能
            results.push(testDataMigration());
            
            // 测试5: 按前缀批量查询
            results.push(testPrefixQuery());
            
            // 测试6: 存储完整性验证
            results.push(testStorageIntegrity());
            
            // 显示测试结果
            displayResults(results);
        }

        /**
         * @function 测试数据结构定义模块
         */
        function testDataSchemas() {
            try {
                const schemas = SmartOffice.Data.Schemas;
                const schemaNames = schemas.getSchemaNames();
                const fieldMappingSchema = schemas.getSchema('FieldMappingTemplate');
                const timeRangeSchema = schemas.getSchema('TimeRangeConfig');
                
                return {
                    name: '数据结构定义模块测试',
                    passed: schemaNames.length >= 4 && fieldMappingSchema && timeRangeSchema,
                    details: `数据结构数量: ${schemaNames.length}, 字段映射结构: ${fieldMappingSchema ? '✓' : '✗'}, 时间段结构: ${timeRangeSchema ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '数据结构定义模块测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试扩展存储方法
         */
        function testExtendedStorageMethods() {
            try {
                const storage = SmartOffice.Core.Storage;
                
                // 测试字段映射模板存储
                const testTemplate = {
                    id: 'mapping_test_001',
                    name: '测试映射模板',
                    mappings: [
                        { sourceField: '销售额', targetField: 'amount', dataType: 'number' }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                const templateSaved = storage.saveFieldMappingTemplate(testTemplate);
                const templates = storage.getAllFieldMappingTemplates();

                // 测试时间段配置存储
                const testTimeRange = {
                    id: 'timerange_test_001',
                    name: '测试时间段',
                    start: '09:00',
                    end: '17:00',
                    type: 'custom',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                const timeRangeSaved = storage.saveTimeRangeConfig(testTimeRange);
                const timeRanges = storage.getAllTimeRangeConfigs();
                
                return {
                    name: '扩展存储方法测试',
                    passed: templateSaved && timeRangeSaved && templates.length > 0 && timeRanges.length > 0,
                    details: `模板保存: ${templateSaved ? '✓' : '✗'}, 时间段保存: ${timeRangeSaved ? '✓' : '✗'}, 模板数量: ${templates.length}, 时间段数量: ${timeRanges.length}`
                };
            } catch (error) {
                return { name: '扩展存储方法测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试数据验证功能
         */
        function testDataValidation() {
            try {
                const schemas = SmartOffice.Data.Schemas;
                
                // 测试有效数据
                const validTemplate = {
                    id: 'mapping_valid_001',
                    name: '有效模板',
                    mappings: [
                        { sourceField: '销售额', targetField: 'amount', dataType: 'number' }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                const validValidation = schemas.validateData(validTemplate, 'FieldMappingTemplate');
                
                // 测试无效数据
                const invalidTemplate = {
                    // 缺少必需字段 id, name, mappings
                    description: '无效模板'
                };
                
                const invalidValidation = schemas.validateData(invalidTemplate, 'FieldMappingTemplate');
                
                return {
                    name: '数据验证功能测试',
                    passed: validValidation.isValid && !invalidValidation.isValid,
                    details: `有效数据验证: ${validValidation.isValid ? '✓' : '✗'}, 无效数据验证: ${!invalidValidation.isValid ? '✓' : '✗'}, 错误数量: ${invalidValidation.errors.length}`
                };
            } catch (error) {
                return { name: '数据验证功能测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试数据迁移功能
         */
        function testDataMigration() {
            try {
                const schemas = SmartOffice.Data.Schemas;
                
                // 模拟旧版本数据
                const oldData = {
                    id: 'mapping_old_001',
                    name: '旧版本模板',
                    mappings: [
                        { sourceField: '销售额', targetField: 'amount' } // 缺少 dataType
                    ]
                };
                
                const migrationResult = schemas.migrateData(oldData, 'FieldMappingTemplate', '0.9.0');
                
                // 检查迁移后的数据是否包含新字段
                const migratedData = migrationResult.migratedData;
                const hasNewFields = migratedData.category && migratedData.usageCount !== undefined;
                const hasFixedMappings = migratedData.mappings && migratedData.mappings[0].dataType;
                
                return {
                    name: '数据迁移功能测试',
                    passed: migrationResult.success && hasNewFields && hasFixedMappings,
                    details: `迁移成功: ${migrationResult.success ? '✓' : '✗'}, 新字段: ${hasNewFields ? '✓' : '✗'}, 修复映射: ${hasFixedMappings ? '✓' : '✗'}, 变更数量: ${migrationResult.changes.length}`
                };
            } catch (error) {
                return { name: '数据迁移功能测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试按前缀批量查询
         */
        function testPrefixQuery() {
            try {
                const storage = SmartOffice.Core.Storage;
                
                // 先保存一些测试数据
                storage.set('field_mapping_test1', { name: '测试1' });
                storage.set('field_mapping_test2', { name: '测试2' });
                storage.set('time_range_test1', { name: '时间段1' });
                storage.set('other_data', { name: '其他数据' });
                
                // 测试按前缀查询
                const mappingItems = storage.getItemsByPrefix('field_mapping_');
                const timeRangeItems = storage.getItemsByPrefix('time_range_');
                const allItems = storage.getItemsByPrefix('');
                
                // 测试分页查询
                const paginatedItems = storage.getItemsByPrefix('field_mapping_', { limit: 1, offset: 0 });
                
                return {
                    name: '按前缀批量查询测试',
                    passed: mappingItems.length >= 2 && timeRangeItems.length >= 1 && paginatedItems.length === 1,
                    details: `映射项: ${mappingItems.length}, 时间段项: ${timeRangeItems.length}, 分页查询: ${paginatedItems.length}, 总项目: ${allItems.length}`
                };
            } catch (error) {
                return { name: '按前缀批量查询测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试存储完整性验证
         */
        function testStorageIntegrity() {
            try {
                const storage = SmartOffice.Core.Storage;
                
                // 保存一些有效和无效的数据进行测试
                const validTemplate = {
                    id: 'mapping_integrity_001',
                    name: '完整性测试模板',
                    mappings: [
                        { sourceField: '销售额', targetField: 'amount', dataType: 'number' }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                storage.saveFieldMappingTemplate(validTemplate);
                
                // 验证存储完整性
                const integrityResult = storage.validateStorageIntegrity('fieldMapping');
                
                // 测试存储优化
                const optimizationResult = storage.optimizeStorage();
                
                return {
                    name: '存储完整性验证测试',
                    passed: integrityResult.totalItems > 0 && optimizationResult.success,
                    details: `总项目: ${integrityResult.totalItems}, 有效项目: ${integrityResult.validItems}, 优化成功: ${optimizationResult.success ? '✓' : '✗'}, 优化项目: ${optimizationResult.optimizedItems}`
                };
            } catch (error) {
                return { name: '存储完整性验证测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 显示测试结果
         */
        function displayResults(results) {
            const container = document.getElementById('testResults');
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;
            
            let html = `<div class="test-result info">
                <strong>测试总结: ${passedCount}/${totalCount} 通过</strong>
                <div class="details">阶段二：存储系统扩展功能验证</div>
            </div>`;
            
            results.forEach(result => {
                html += `<div class="test-result ${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.name}:</strong> ${result.passed ? '通过' : '失败'}
                    <div class="details">${result.details}</div>
                </div>`;
            });
            
            container.innerHTML = html;
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
