<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>CSS样式测试页面</title>
    
    <!-- CSS文件 - 按修复后的顺序加载 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/ios-theme.css">
    <link rel="stylesheet" href="src/css/mobile.css">
    <link rel="stylesheet" href="src/css/components/file-upload-page.css">
    <link rel="stylesheet" href="src/css/components/config-list.css">
    <link rel="stylesheet" href="src/css/components/toast.css">
    <link rel="stylesheet" href="src/css/components/charts.css">
</head>
<body>
    <!-- iOS风格状态栏占位 -->
    <div class="status-bar-spacer"></div>
    
    <!-- 主应用容器 -->
    <div id="app" class="app-container">
        <!-- 导航栏测试 -->
        <header class="nav-bar">
            <div class="nav-content">
                <button type="button" class="nav-button nav-back" title="返回">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
                <h1 class="nav-title">CSS样式测试</h1>
                <button type="button" class="nav-button nav-add" title="新建">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
            </div>
        </header>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="page">
                <div class="page-content">
                    <!-- 欢迎区域测试 -->
                    <div class="welcome-section">
                        <div class="welcome-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                        </div>
                        <h2 class="welcome-title">CSS样式测试</h2>
                        <p class="welcome-description">测试所有CSS变量和样式是否正确应用</p>
                    </div>

                    <!-- 文件上传区域测试 -->
                    <div class="file-upload-section">
                        <div id="fileUploadContainer">
                            <div style="padding: 20px; text-align: center; color: var(--text-secondary);">
                                文件上传组件区域
                            </div>
                        </div>
                    </div>

                    <!-- 配置预览测试 -->
                    <div class="saved-configs-section">
                        <div class="section-header">
                            <h3 class="section-title">配置预览测试</h3>
                            <button type="button" class="section-action-btn" title="管理">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                </svg>
                            </button>
                        </div>

                        <!-- 配置预览卡片测试 -->
                        <div class="config-preview-list">
                            <div class="config-preview-card">
                                <div class="config-preview-header">
                                    <h4 class="config-preview-name">测试配置1</h4>
                                    <div class="config-preview-actions">
                                        <button class="config-preview-btn edit-btn">
                                            <svg viewBox="0 0 24 24">
                                                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                                            </svg>
                                        </button>
                                        <button class="config-preview-btn delete-btn">
                                            <svg viewBox="0 0 24 24">
                                                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <p class="config-preview-description">这是一个测试配置的描述信息</p>
                                <div class="config-preview-meta">
                                    <span class="config-preview-date">2025-06-16</span>
                                    <span class="config-preview-fields">5个字段</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- iOS按钮测试 -->
                    <div style="margin-top: 24px;">
                        <h3 style="margin-bottom: 16px; color: var(--text-primary);">iOS按钮测试</h3>
                        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                            <button class="ios-button ios-button-primary">主要按钮</button>
                            <button class="ios-button ios-button-secondary">次要按钮</button>
                            <button class="ios-button ios-button-destructive">删除按钮</button>
                            <button class="ios-button ios-button-plain">纯文本按钮</button>
                        </div>
                    </div>

                    <!-- 图表容器测试 -->
                    <div class="chart-container" style="margin-top: 24px;">
                        <div class="chart-header">
                            <div>
                                <h3 class="chart-title">图表测试</h3>
                                <p class="chart-subtitle">测试图表样式是否正确</p>
                            </div>
                            <div class="chart-actions">
                                <button class="chart-action-button">设置</button>
                                <button class="chart-action-button">导出</button>
                            </div>
                        </div>
                        <div class="chart-canvas-container">
                            <div style="text-align: center; color: var(--text-secondary);">
                                图表内容区域
                            </div>
                        </div>
                    </div>

                    <!-- 空状态测试 -->
                    <div class="empty-configs-state" style="margin-top: 24px;">
                        <div class="empty-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                            </svg>
                        </div>
                        <p class="empty-description">空状态样式测试<br>所有CSS变量应该正确显示</p>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Toast测试 -->
        <div class="toast-container">
            <div class="ios-toast show">
                <svg class="toast-icon" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
                Toast消息测试
            </div>
        </div>
    </div>
    
    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CSS测试页面加载完成');
            
            // 检查CSS变量是否正确应用
            const testElement = document.querySelector('.welcome-section');
            const computedStyle = getComputedStyle(testElement);
            
            console.log('背景色:', computedStyle.background);
            console.log('边距:', computedStyle.marginBottom);
            
            // 测试按钮点击
            document.querySelectorAll('button').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('按钮点击测试:', this.textContent || '图标按钮');
                });
            });
        });
    </script>
</body>
</html>
