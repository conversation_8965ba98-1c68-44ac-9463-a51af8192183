<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>映射管理界面测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .details { margin-top: 5px; font-size: 12px; opacity: 0.8; }
        .test-container { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .demo-button { 
            background: #007aff; color: white; border: none; border-radius: 8px; 
            padding: 10px 20px; margin: 5px; cursor: pointer; 
        }
        .demo-button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>映射管理界面 - 阶段三测试</h1>
    <div id="testResults"></div>

    <!-- 功能演示区域 -->
    <div class="test-container">
        <h3>功能演示</h3>
        <button class="demo-button" onclick="showMappingPage()">显示字段映射管理页面</button>
        <button class="demo-button" onclick="showMappingDialog()">显示映射配置对话框</button>
        <button class="demo-button" onclick="testTouchInteraction()">测试触摸交互</button>
        <button class="demo-button" onclick="testResponsiveDesign()">测试响应式设计</button>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="../src/js/core/smartoffice-core.js"></script>
    <script src="../src/js/core/smartoffice-events.js"></script>
    <script src="../src/js/core/smartoffice-storage.js"></script>
    <script src="../src/js/utils/smartoffice-helpers.js"></script>
    <script src="../src/js/data/smartoffice-data-schemas.js"></script>
    <script src="../src/js/data/smartoffice-field-mapper.js"></script>
    <script src="../src/js/data/smartoffice-timerange-manager.js"></script>
    <script src="../src/js/components/smartoffice-field-mapping-page.js"></script>
    <script src="../src/js/components/smartoffice-mapping-dialog.js"></script>

    <!-- 引入CSS样式 -->
    <link rel="stylesheet" href="../src/css/components/field-mapping-page.css">
    <link rel="stylesheet" href="../src/css/components/mapping-dialog.css">

    <script>
        /**
         * @function 运行映射管理界面测试
         */
        function runTests() {
            const results = [];
            
            // 测试1: 组件加载
            results.push(testComponentLoading());
            
            // 测试2: CSS样式加载
            results.push(testCSSLoading());
            
            // 测试3: 字段映射页面组件
            results.push(testFieldMappingPageComponent());
            
            // 测试4: 映射配置对话框组件
            results.push(testMappingDialogComponent());
            
            // 测试5: iOS设计规范
            results.push(testIOSDesignCompliance());
            
            // 测试6: 触摸交互
            results.push(testTouchInteraction());
            
            // 显示测试结果
            displayResults(results);
        }

        /**
         * @function 测试组件加载
         */
        function testComponentLoading() {
            try {
                const fieldMappingPageExists = typeof SmartOffice?.Components?.FieldMappingPage !== 'undefined';
                const mappingDialogExists = typeof SmartOffice?.Components?.MappingDialog !== 'undefined';
                const fieldMapperExists = typeof SmartOffice?.Data?.FieldMapper !== 'undefined';
                const schemasExists = typeof SmartOffice?.Data?.Schemas !== 'undefined';
                
                return {
                    name: '组件加载测试',
                    passed: fieldMappingPageExists && mappingDialogExists && fieldMapperExists && schemasExists,
                    details: `字段映射页面: ${fieldMappingPageExists ? '✓' : '✗'}, 映射对话框: ${mappingDialogExists ? '✓' : '✗'}, 字段映射器: ${fieldMapperExists ? '✓' : '✗'}, 数据结构: ${schemasExists ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '组件加载测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试CSS样式加载
         */
        function testCSSLoading() {
            try {
                // 检查CSS规则是否加载
                const stylesheets = document.styleSheets;
                let fieldMappingCSSLoaded = false;
                let mappingDialogCSSLoaded = false;

                for (let i = 0; i < stylesheets.length; i++) {
                    const stylesheet = stylesheets[i];
                    if (stylesheet.href && stylesheet.href.includes('field-mapping-page.css')) {
                        fieldMappingCSSLoaded = true;
                    }
                    if (stylesheet.href && stylesheet.href.includes('mapping-dialog.css')) {
                        mappingDialogCSSLoaded = true;
                    }
                }

                return {
                    name: 'CSS样式加载测试',
                    passed: fieldMappingCSSLoaded && mappingDialogCSSLoaded,
                    details: `字段映射页面样式: ${fieldMappingCSSLoaded ? '✓' : '✗'}, 映射对话框样式: ${mappingDialogCSSLoaded ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: 'CSS样式加载测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试字段映射页面组件
         */
        function testFieldMappingPageComponent() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.id = 'testFieldMappingPage';
                testContainer.style.display = 'none';
                document.body.appendChild(testContainer);

                // 初始化组件
                const component = new SmartOffice.Components.FieldMappingPage({
                    containerId: 'testFieldMappingPage'
                });
                component.init();

                // 检查组件是否正确初始化
                const hasContainer = component.container !== null;
                const hasFieldMapper = component.fieldMapper !== null;
                const hasStorage = component.storage !== null;

                // 清理
                document.body.removeChild(testContainer);

                return {
                    name: '字段映射页面组件测试',
                    passed: hasContainer && hasFieldMapper && hasStorage,
                    details: `容器: ${hasContainer ? '✓' : '✗'}, 字段映射器: ${hasFieldMapper ? '✓' : '✗'}, 存储: ${hasStorage ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '字段映射页面组件测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试映射配置对话框组件
         */
        function testMappingDialogComponent() {
            try {
                // 初始化组件
                const component = new SmartOffice.Components.MappingDialog({
                    onSave: function() {},
                    onCancel: function() {}
                });
                component.init();

                // 检查组件是否正确初始化
                const hasDialog = component.dialog !== null;
                const hasOverlay = component.overlay !== null;
                const hasFieldMapper = component.fieldMapper !== null;

                return {
                    name: '映射配置对话框组件测试',
                    passed: hasDialog && hasOverlay && hasFieldMapper,
                    details: `对话框: ${hasDialog ? '✓' : '✗'}, 遮罩层: ${hasOverlay ? '✓' : '✗'}, 字段映射器: ${hasFieldMapper ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '映射配置对话框组件测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试iOS设计规范
         */
        function testIOSDesignCompliance() {
            try {
                // 创建测试按钮检查最小触摸区域
                const testButton = document.createElement('button');
                testButton.className = 'btn-primary';
                testButton.style.position = 'absolute';
                testButton.style.left = '-9999px';
                document.body.appendChild(testButton);

                const computedStyle = window.getComputedStyle(testButton);
                const minHeight = parseInt(computedStyle.minHeight);
                const padding = parseInt(computedStyle.paddingTop) + parseInt(computedStyle.paddingBottom);
                const totalHeight = minHeight + padding;

                // 检查是否满足44px最小触摸区域
                const meetsMinTouchArea = totalHeight >= 44;

                // 检查圆角设计
                const borderRadius = parseInt(computedStyle.borderRadius);
                const hasRoundedCorners = borderRadius >= 8;

                // 清理
                document.body.removeChild(testButton);

                return {
                    name: 'iOS设计规范测试',
                    passed: meetsMinTouchArea && hasRoundedCorners,
                    details: `最小触摸区域(${totalHeight}px): ${meetsMinTouchArea ? '✓' : '✗'}, 圆角设计(${borderRadius}px): ${hasRoundedCorners ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: 'iOS设计规范测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 测试触摸交互
         */
        function testTouchInteraction() {
            try {
                // 检查触摸事件支持
                const supportsTouchEvents = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                
                // 检查CSS hover状态
                const testElement = document.createElement('div');
                testElement.className = 'mapping-template-card';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);

                const hasHoverEffects = window.getComputedStyle(testElement).transition.includes('transform');

                // 清理
                document.body.removeChild(testElement);

                return {
                    name: '触摸交互测试',
                    passed: supportsTouchEvents || hasHoverEffects,
                    details: `触摸事件支持: ${supportsTouchEvents ? '✓' : '✗'}, 悬停效果: ${hasHoverEffects ? '✓' : '✗'}`
                };
            } catch (error) {
                return { name: '触摸交互测试', passed: false, details: error.message };
            }
        }

        /**
         * @function 显示测试结果
         */
        function displayResults(results) {
            const container = document.getElementById('testResults');
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;
            
            let html = `<div class="test-result info">
                <strong>测试总结: ${passedCount}/${totalCount} 通过</strong>
                <div class="details">阶段三：映射管理界面开发功能验证</div>
            </div>`;
            
            results.forEach(result => {
                html += `<div class="test-result ${result.passed ? 'pass' : 'fail'}">
                    <strong>${result.name}:</strong> ${result.passed ? '通过' : '失败'}
                    <div class="details">${result.details}</div>
                </div>`;
            });
            
            container.innerHTML = html;
        }

        /**
         * @function 演示功能
         */
        function showMappingPage() {
            alert('字段映射管理页面演示功能（需要完整的HTML结构支持）');
        }

        function showMappingDialog() {
            try {
                const dialog = new SmartOffice.Components.MappingDialog({
                    onSave: function(template) {
                        alert('模板保存成功: ' + template.name);
                    },
                    onCancel: function() {
                        alert('对话框已取消');
                    }
                });
                dialog.init();
                dialog.show();
            } catch (error) {
                alert('对话框演示失败: ' + error.message);
            }
        }

        function testTouchInteraction() {
            alert('触摸交互测试：请在移动设备上测试触摸反馈效果');
        }

        function testResponsiveDesign() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            const isTablet = width > 768 && width <= 1024;
            const isDesktop = width > 1024;
            
            alert(`响应式设计测试:\n当前屏幕宽度: ${width}px\n设备类型: ${isMobile ? '移动端' : isTablet ? '平板' : '桌面端'}`);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
